<?php

namespace DD\App\Admin;

class PhoneFieldSettings
{
    private const OPTION_NAME = 'dd_gr_cf7_phone_field_id';

    public function getPhoneFieldId(): string
    {
        return (string) get_option(self::OPTION_NAME, '');
    }

    public function savePhoneFieldId(string $fieldId): bool
    {
        return update_option(self::OPTION_NAME, sanitize_text_field($fieldId));
    }

    public function deletePhoneFieldId(): bool
    {
        return delete_option(self::OPTION_NAME);
    }

    public function hasPhoneFieldId(): bool
    {
        return !empty($this->getPhoneFieldId());
    }
}
