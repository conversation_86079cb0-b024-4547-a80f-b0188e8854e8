<?php

namespace DD\App\Models;

class GetResponseContact
{
    public string $email;
    public ?string $name;
    public ?string $ipAddress;
    public ?string $phone;

    public function __construct(string $email, ?string $name = null, ?string $ipAddress = null, ?string $phone = null)
    {
        $this->email = $email;
        $this->name = $name;
        $this->ipAddress = $ipAddress;
        $this->phone = $phone;
    }

    /** @return array{email: string, name?: string, ipAddress?: string, customFieldValues?: array} */
    public function toApiArray(?string $phoneFieldId = null): array
    {
        $data = ['email' => $this->email];

        if ($this->name !== null && $this->name !== '') {
            $data['name'] = $this->name;
        }

        if ($this->ipAddress !== null && $this->ipAddress !== '') {
            $data['ipAddress'] = $this->ipAddress;
        }

        if ($this->phone !== null && $this->phone !== '' && $phoneFieldId !== null) {
            $data['customFieldValues'] = [
                [
                    'customFieldId' => $phoneFieldId,
                    'value' => [$this->phone]
                ]
            ];
        }

        return $data;
    }
}
