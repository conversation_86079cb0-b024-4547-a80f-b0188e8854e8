<?php

use PHPUnit\Framework\TestCase;
use DD\App\Models\GetResponseContact;
use DD\App\Admin\PhoneFieldSettings;

class PhoneFieldTest extends TestCase
{
    public function testGetResponseContactWithPhone(): void
    {
        $contact = new GetResponseContact(
            '<EMAIL>',
            '<PERSON>',
            '***********',
            '+48123456789'
        );

        $this->assertEquals('<EMAIL>', $contact->email);
        $this->assertEquals('<PERSON>', $contact->name);
        $this->assertEquals('***********', $contact->ipAddress);
        $this->assertEquals('+48123456789', $contact->phone);
    }

    public function testToApiArrayWithPhoneField(): void
    {
        $contact = new GetResponseContact(
            '<EMAIL>',
            '<PERSON>',
            '***********',
            '+48123456789'
        );

        $apiArray = $contact->toApiArray('phone_field_id');

        $this->assertEquals('<EMAIL>', $apiArray['email']);
        $this->assertEquals('<PERSON>', $apiArray['name']);
        $this->assertEquals('***********', $apiArray['ipAddress']);
        
        $this->assertArrayHasKey('customFieldValues', $apiArray);
        $this->assertCount(1, $apiArray['customFieldValues']);
        $this->assertEquals('phone_field_id', $apiArray['customFieldValues'][0]['customFieldId']);
        $this->assertEquals(['+48123456789'], $apiArray['customFieldValues'][0]['value']);
    }

    public function testToApiArrayWithoutPhoneField(): void
    {
        $contact = new GetResponseContact(
            '<EMAIL>',
            'John Doe',
            '***********',
            '+48123456789'
        );

        $apiArray = $contact->toApiArray(null);

        $this->assertEquals('<EMAIL>', $apiArray['email']);
        $this->assertEquals('John Doe', $apiArray['name']);
        $this->assertEquals('***********', $apiArray['ipAddress']);
        
        $this->assertArrayNotHasKey('customFieldValues', $apiArray);
    }

    public function testToApiArrayWithoutPhone(): void
    {
        $contact = new GetResponseContact(
            '<EMAIL>',
            'John Doe',
            '***********',
            null
        );

        $apiArray = $contact->toApiArray('phone_field_id');

        $this->assertEquals('<EMAIL>', $apiArray['email']);
        $this->assertEquals('John Doe', $apiArray['name']);
        $this->assertEquals('***********', $apiArray['ipAddress']);
        
        $this->assertArrayNotHasKey('customFieldValues', $apiArray);
    }

    public function testPhoneFieldSettings(): void
    {
        // Mockowanie funkcji WordPress
        if (!function_exists('get_option')) {
            function get_option($option, $default = false) {
                return $default;
            }
        }
        
        if (!function_exists('update_option')) {
            function update_option($option, $value) {
                return true;
            }
        }
        
        if (!function_exists('sanitize_text_field')) {
            function sanitize_text_field($str) {
                return $str;
            }
        }

        $settings = new PhoneFieldSettings();
        
        $this->assertFalse($settings->hasPhoneFieldId());
        $this->assertEquals('', $settings->getPhoneFieldId());
        $this->assertTrue($settings->savePhoneFieldId('test_field_id'));
    }
}
